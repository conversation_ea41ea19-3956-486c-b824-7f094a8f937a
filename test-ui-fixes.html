<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #3b82f6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>江西干部网络学院学习助手 - UI修复测试</h1>
        
        <div class="test-section">
            <h3>1. 显示/隐藏设置界面按钮</h3>
            <p>测试页面右上角是否有圆形的"设"按钮，点击可以显示/隐藏设置面板</p>
            <div class="status" id="toggle-btn-status">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>2. courseDetails页面处理器</h3>
            <p>测试课程详情页面处理器是否已实现</p>
            <div class="status" id="course-details-status">等待测试...</div>
            <button class="test-button" onclick="testCourseDetailsHandler()">测试处理器</button>
        </div>
        
        <div class="test-section">
            <h3>3. 账号管理样式</h3>
            <p>测试账号管理是否为手机号码+密码的录入样式</p>
            <div class="status" id="account-style-status">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>4. 设置配置控件</h3>
            <p>测试"自动静音视频"和"自动添加选修课程"复选框是否存在</p>
            <div class="status" id="settings-controls-status">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>5. 日志功能</h3>
            <p>测试"清空日志"和"导出日志"功能是否正常</p>
            <div class="status" id="log-functions-status">等待测试...</div>
            <button class="test-button" onclick="testLogFunctions()">测试日志功能</button>
        </div>
        
        <div class="test-section">
            <h3>6. URL变化监听</h3>
            <p>测试页面跳转后是否能自动检测并处理新页面</p>
            <div class="status" id="url-change-status">等待测试...</div>
            <button class="test-button" onclick="testUrlChangeDetection()">测试URL监听</button>
        </div>
    </div>

    <script>
        // 检查切换按钮
        function checkToggleButton() {
            const toggleBtn = document.querySelector('.auto-learning-toggle-btn');
            const status = document.getElementById('toggle-btn-status');
            
            if (toggleBtn) {
                status.textContent = '✓ 切换按钮已找到';
                status.className = 'status success';
            } else {
                status.textContent = '✗ 未找到切换按钮';
                status.className = 'status error';
            }
        }

        // 测试课程详情处理器
        function testCourseDetailsHandler() {
            const status = document.getElementById('course-details-status');
            
            if (window.autoLearningBot && 
                window.autoLearningBot.pageHandlers && 
                window.autoLearningBot.pageHandlers.has('courseDetails')) {
                status.textContent = '✓ courseDetails处理器已实现';
                status.className = 'status success';
            } else {
                status.textContent = '✗ courseDetails处理器未找到';
                status.className = 'status error';
            }
        }

        // 测试日志功能
        function testLogFunctions() {
            const status = document.getElementById('log-functions-status');
            
            if (window.uiManager && 
                typeof window.uiManager.clearLogs === 'function' &&
                typeof window.uiManager.exportLogs === 'function') {
                status.textContent = '✓ 日志功能已实现';
                status.className = 'status success';
            } else {
                status.textContent = '✗ 日志功能未找到';
                status.className = 'status error';
            }
        }

        // 测试URL变化检测
        function testUrlChangeDetection() {
            const status = document.getElementById('url-change-status');
            
            if (window.autoLearningBot && 
                typeof window.autoLearningBot.setupUrlChangeListener === 'function') {
                status.textContent = '✓ URL变化监听已实现';
                status.className = 'status success';
            } else {
                status.textContent = '✗ URL变化监听未找到';
                status.className = 'status error';
            }
        }

        // 检查账号管理样式
        function checkAccountStyle() {
            const status = document.getElementById('account-style-status');
            
            // 这个需要在实际的脚本环境中测试
            status.textContent = '⚠ 需要在脚本环境中测试';
            status.className = 'status warning';
        }

        // 检查设置控件
        function checkSettingsControls() {
            const status = document.getElementById('settings-controls-status');
            
            // 这个需要在实际的脚本环境中测试
            status.textContent = '⚠ 需要在脚本环境中测试';
            status.className = 'status warning';
        }

        // 页面加载时执行检查
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkToggleButton();
                checkAccountStyle();
                checkSettingsControls();
            }, 1000);
        });

        // 定期检查脚本是否加载
        setInterval(() => {
            if (window.autoLearningBot) {
                testCourseDetailsHandler();
                testUrlChangeDetection();
            }
            if (window.uiManager) {
                testLogFunctions();
            }
        }, 2000);
    </script>
</body>
</html>
