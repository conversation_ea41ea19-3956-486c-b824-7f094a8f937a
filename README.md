# 江西干部网络学院自动学习助手 - Tampermonkey版

## 简介

这是一个专为江西干部网络学院开发的自动化学习助手，已从浏览器插件转换为Tampermonkey脚本。支持多账号管理、智能验证码识别、自动课程学习等功能。

## 功能特性

### 🎯 核心功能
- **多账号管理**：支持添加多个学习账号，批量处理
- **智能验证码识别**：集成百度OCR API，自动识别登录验证码
- **自动化学习流程**：自动登录、选课、观看视频、切换课件
- **学习进度跟踪**：实时显示学习状态和进度
- **智能重试机制**：遇到错误自动重试，提高成功率

### 🛠️ 高级功能
- **视频自动静音**：避免学习过程中的声音干扰
- **自动选修课程**：根据设置自动添加指定数量的选修课
- **学习状态检测**：自动检测学习完成状态
- **详细日志记录**：记录所有操作过程，便于问题排查

## 安装使用

### 1. 安装Tampermonkey
- Chrome: 从Chrome Web Store安装Tampermonkey扩展
- Firefox: 从Firefox Add-ons安装Tampermonkey扩展
- Edge: 从Microsoft Edge Add-ons安装Tampermonkey扩展

### 2. 安装脚本
1. 打开Tampermonkey管理面板
2. 点击"创建新脚本"
3. 复制`tampermonkey-script.user.js`文件内容
4. 粘贴到编辑器中
5. 按Ctrl+S保存脚本

### 3. 配置百度OCR API
1. 访问[百度智能云](https://cloud.baidu.com/)
2. 注册账号并创建文字识别应用
3. 获取API Key和Secret Key
4. 在脚本设置中填入API密钥

### 4. 使用步骤
1. 访问江西干部网络学院网站
2. 点击Tampermonkey菜单中的"账号管理"
3. 添加学习账号（手机号+密码）
4. 配置学习参数（延时时间、课程数量等）
5. 点击"开始学习"

## 菜单功能

### 📚 开始学习
启动自动学习流程，按顺序处理账号队列

### ⏹️ 停止学习
停止当前学习任务

### 👥 账号管理
- 添加/删除学习账号
- 查看账号状态
- 批量管理功能

### ⚙️ 设置配置
- 百度OCR API配置
- 学习参数设置
- 功能开关控制

### 📋 查看日志
- 实时查看运行日志
- 问题诊断信息
- 操作历史记录

## 配置说明

### 基础设置
- **操作延时时间**：每个操作之间的等待时间（0.5-10秒）
- **自动选修课程数量**：自动添加的选修课数量（1-50门）
- **自动静音视频**：学习时自动将视频静音
- **自动添加选修课程**：必修课完成后自动添加选修课

### API配置
- **百度API Key**：百度智能云文字识别API Key
- **百度Secret Key**：百度智能云文字识别Secret Key

## 学习流程

1. **登录阶段**
   - 自动填写账号密码
   - 智能识别验证码
   - 处理登录错误

2. **课程选择**
   - 优先处理必修课程
   - 必修课完成后处理选修课
   - 自动添加选修课程（如开启）

3. **视频学习**
   - 自动播放视频
   - 智能静音处理
   - 监听播放完成
   - 自动切换下一课件

4. **完成处理**
   - 检测学习完成状态
   - 自动退出当前账号
   - 处理下一个账号

## 注意事项

### ⚠️ 重要提醒
- 本脚本仅供学习交流使用
- 请遵守网站使用条款
- 建议合理设置延时时间，避免请求过于频繁
- 定期检查学习进度，确保学习质量

### 🔧 故障排除
- **验证码识别失败**：检查百度API配置是否正确
- **登录失败**：确认账号密码正确，检查网络连接
- **页面加载超时**：增加延时时间设置
- **脚本无响应**：刷新页面重新启动脚本

### 📞 技术支持
如遇到问题，请查看日志信息进行诊断，或联系开发者获取技术支持。

## 更新日志

### v2.0.0
- 从浏览器插件转换为Tampermonkey脚本
- 优化用户界面和交互体验
- 增强错误处理和重试机制
- 改进验证码识别准确率
- 添加详细的日志记录功能

## 开源协议

本项目采用MIT开源协议，欢迎贡献代码和提出建议。
