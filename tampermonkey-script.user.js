// ==UserScript==
// @name         江西干部网络学院自动学习助手
// @namespace    https://study.jxgbwlxy.gov.cn/
// @version      2.0.0
// @description  专业的自动化学习助手，支持多账号管理、智能验证码识别、自动课程学习
// <AUTHOR> Assistant
// @match        https://study.jxgbwlxy.gov.cn/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_deleteValue
// @grant        GM_listValues
// @grant        GM_xmlhttpRequest
// @grant        GM_registerMenuCommand
// @grant        GM_unregisterMenuCommand
// @grant        GM_notification
// @grant        GM_addStyle
// @connect      aip.baidubce.com
// @run-at       document-end
// ==/UserScript==

(function() {
    'use strict';

    // ==================== 样式定义 ====================
    GM_addStyle(`
        .auto-learning-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 400px;
            max-height: 600px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            overflow: hidden;
        }
        
        .auto-learning-header {
            background: #409eff;
            color: white;
            padding: 12px 16px;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .auto-learning-close {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .auto-learning-content {
            padding: 16px;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .auto-learning-tabs {
            display: flex;
            border-bottom: 1px solid #eee;
            margin-bottom: 16px;
        }
        
        .auto-learning-tab {
            padding: 8px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            color: #666;
        }
        
        .auto-learning-tab.active {
            color: #409eff;
            border-bottom-color: #409eff;
        }
        
        .auto-learning-form-group {
            margin-bottom: 16px;
        }
        
        .auto-learning-label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #333;
        }
        
        .auto-learning-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .auto-learning-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 8px;
        }
        
        .auto-learning-button:hover {
            background: #66b1ff;
        }
        
        .auto-learning-button.danger {
            background: #f56c6c;
        }
        
        .auto-learning-button.danger:hover {
            background: #f78989;
        }
        
        .auto-learning-log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 12px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .auto-learning-log-item {
            margin-bottom: 4px;
        }
        
        .auto-learning-log-time {
            color: #666;
            margin-right: 8px;
        }
        
        .auto-learning-log-level-info { color: #409eff; }
        .auto-learning-log-level-success { color: #67c23a; }
        .auto-learning-log-level-warning { color: #e6a23c; }
        .auto-learning-log-level-error { color: #f56c6c; }
        
        .auto-learning-account-item {
            background: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 12px;
            margin-bottom: 8px;
        }
        
        .auto-learning-account-phone {
            font-weight: 500;
            margin-bottom: 4px;
        }
        
        .auto-learning-account-status {
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 2px;
            color: white;
        }
        
        .auto-learning-status-pending { background: #909399; }
        .auto-learning-status-running { background: #409eff; }
        .auto-learning-status-completed { background: #67c23a; }
        .auto-learning-status-error { background: #f56c6c; }
        
        .auto-learning-checkbox {
            margin-right: 8px;
        }
        
        .auto-learning-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .auto-learning-col {
            flex: 1;
            margin-right: 8px;
        }
        
        .auto-learning-col:last-child {
            margin-right: 0;
        }
    `);

    // ==================== 存储管理类 ====================
    class StorageManager {
        static async get(key, defaultValue = null) {
            try {
                const value = GM_getValue(key);
                return value !== undefined ? JSON.parse(value) : defaultValue;
            } catch (error) {
                console.error('Storage get error:', error);
                return defaultValue;
            }
        }

        static async set(key, value) {
            try {
                GM_setValue(key, JSON.stringify(value));
                return true;
            } catch (error) {
                console.error('Storage set error:', error);
                return false;
            }
        }

        static async remove(key) {
            try {
                GM_deleteValue(key);
                return true;
            } catch (error) {
                console.error('Storage remove error:', error);
                return false;
            }
        }

        static async clear() {
            try {
                const keys = GM_listValues();
                keys.forEach(key => GM_deleteValue(key));
                return true;
            } catch (error) {
                console.error('Storage clear error:', error);
                return false;
            }
        }
    }

    // ==================== 日志系统类 ====================
    class Logger {
        constructor() {
            this.logs = [];
            this.maxLogs = 1000;
            this.loadLogs();
        }

        async loadLogs() {
            this.logs = await StorageManager.get('logs_data', []);
        }

        async saveLogs() {
            // 限制日志数量
            if (this.logs.length > this.maxLogs) {
                this.logs = this.logs.slice(-this.maxLogs);
            }
            await StorageManager.set('logs_data', this.logs);
        }

        log(level, message, category = 'general') {
            const logEntry = {
                id: Date.now() + Math.random(),
                time: new Date().toLocaleTimeString('zh-CN', {
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                }),
                level,
                category,
                message,
                timestamp: Date.now()
            };

            this.logs.push(logEntry);
            this.saveLogs();

            // 控制台输出
            const consoleMethod = level === 'error' ? 'error' : 
                                level === 'warning' ? 'warn' : 'log';
            console[consoleMethod](`[${category}] ${message}`);

            // 更新UI显示
            this.updateLogDisplay();

            return logEntry;
        }

        info(message, category) {
            return this.log('info', message, category);
        }

        success(message, category) {
            return this.log('success', message, category);
        }

        warning(message, category) {
            return this.log('warning', message, category);
        }

        error(message, category) {
            return this.log('error', message, category);
        }

        updateLogDisplay() {
            const logContainer = document.querySelector('.auto-learning-log');
            if (!logContainer) return;

            const recentLogs = this.logs.slice(-50); // 只显示最近50条
            logContainer.innerHTML = recentLogs.map(log => `
                <div class="auto-learning-log-item">
                    <span class="auto-learning-log-time">${log.time}</span>
                    <span class="auto-learning-log-level-${log.level}">[${log.level.toUpperCase()}]</span>
                    <span>${log.message}</span>
                </div>
            `).join('');

            // 滚动到底部
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        clear() {
            this.logs = [];
            this.saveLogs();
            this.updateLogDisplay();
        }
    }

    // ==================== 工具函数类 ====================
    class Utils {
        /**
         * 等待元素出现
         */
        static waitForElement(selector, timeout = 10000, parent = document) {
            return new Promise((resolve, reject) => {
                const element = parent.querySelector(selector);
                if (element) {
                    resolve(element);
                    return;
                }

                const observer = new MutationObserver((mutations, obs) => {
                    const element = parent.querySelector(selector);
                    if (element) {
                        obs.disconnect();
                        resolve(element);
                    }
                });

                observer.observe(parent, {
                    childList: true,
                    subtree: true
                });

                setTimeout(() => {
                    observer.disconnect();
                    reject(new Error(`等待元素超时: ${selector}`));
                }, timeout);
            });
        }

        /**
         * 等待页面加载完成
         */
        static waitForPageLoad(timeout = 30000) {
            return new Promise((resolve, reject) => {
                if (document.readyState === 'complete') {
                    resolve();
                    return;
                }

                const onLoad = () => {
                    document.removeEventListener('DOMContentLoaded', onLoad);
                    window.removeEventListener('load', onLoad);
                    resolve();
                };

                document.addEventListener('DOMContentLoaded', onLoad);
                window.addEventListener('load', onLoad);

                setTimeout(() => {
                    document.removeEventListener('DOMContentLoaded', onLoad);
                    window.removeEventListener('load', onLoad);
                    reject(new Error('页面加载超时'));
                }, timeout);
            });
        }

        /**
         * 安全点击元素
         */
        static async safeClick(element) {
            if (!element) throw new Error('元素不存在');

            // 滚动到元素位置
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await this.sleep(500);

            // 触发点击事件 - 移除view参数避免兼容性问题
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        }

        /**
         * 安全输入文本
         */
        static async safeInput(element, text) {
            if (!element) throw new Error('元素不存在');

            // 清空现有内容
            element.value = '';
            element.focus();

            // 逐字符输入
            for (let char of text) {
                element.value += char;
                element.dispatchEvent(new Event('input', { bubbles: true }));
                await this.sleep(50);
            }

            // 触发change事件
            element.dispatchEvent(new Event('change', { bubbles: true }));
        }

        /**
         * 休眠函数
         */
        static sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        /**
         * 获取元素文本
         */
        static getText(element) {
            if (!element) return '';
            return element.textContent || element.innerText || '';
        }

        /**
         * 获取所有匹配元素
         */
        static getAllElements(selector, parent = document) {
            return Array.from(parent.querySelectorAll(selector));
        }

        /**
         * 等待URL变化
         */
        static waitForUrlChange(expectedUrl, timeout = 10000) {
            return new Promise((resolve, reject) => {
                const checkUrl = () => {
                    if (window.location.href.includes(expectedUrl)) {
                        resolve();
                    } else {
                        setTimeout(checkUrl, 500);
                    }
                };

                checkUrl();

                setTimeout(() => {
                    reject(new Error(`URL变化超时: ${expectedUrl}`));
                }, timeout);
            });
        }

        /**
         * 获取图片Base64
         */
        static getImageBase64(imgElement) {
            return new Promise((resolve, reject) => {
                try {
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');

                    canvas.width = imgElement.naturalWidth || imgElement.width;
                    canvas.height = imgElement.naturalHeight || imgElement.height;

                    ctx.drawImage(imgElement, 0, 0);
                    const base64 = canvas.toDataURL('image/png').split(',')[1];
                    resolve(base64);
                } catch (error) {
                    reject(error);
                }
            });
        }

        /**
         * 获取页面信息
         */
        static getPageInfo() {
            return {
                url: window.location.href,
                title: document.title,
                timestamp: Date.now()
            };
        }
    }

    // ==================== 验证码识别类 ====================
    class CaptchaRecognizer {
        constructor() {
            this.accessToken = null;
            this.tokenExpiry = 0;
        }

        async getAccessToken(ak, sk) {
            try {
                // 检查缓存的token是否有效
                if (this.accessToken && Date.now() < this.tokenExpiry) {
                    return this.accessToken;
                }

                return new Promise((resolve, reject) => {
                    GM_xmlhttpRequest({
                        method: 'POST',
                        url: `https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id=${ak}&client_secret=${sk}`,
                        onload: (response) => {
                            try {
                                const data = JSON.parse(response.responseText);
                                if (data.access_token) {
                                    this.accessToken = data.access_token;
                                    this.tokenExpiry = Date.now() + (data.expires_in - 300) * 1000; // 提前5分钟过期
                                    resolve(this.accessToken);
                                } else {
                                    reject(new Error(data.error_description || '获取访问令牌失败'));
                                }
                            } catch (error) {
                                reject(error);
                            }
                        },
                        onerror: (error) => {
                            reject(new Error('网络请求失败'));
                        }
                    });
                });
            } catch (error) {
                console.error('获取访问令牌失败:', error);
                throw error;
            }
        }

        async recognizeCaptcha(imageBase64, ak, sk) {
            try {
                const accessToken = await this.getAccessToken(ak, sk);

                return new Promise((resolve, reject) => {
                    GM_xmlhttpRequest({
                        method: 'POST',
                        url: `https://aip.baidubce.com/rest/2.0/ocr/v1/accurate_basic?access_token=${accessToken}`,
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded'
                        },
                        data: `image=${encodeURIComponent(imageBase64)}&detect_direction=false&paragraph=false&probability=false&multidirectional_recognize=false`,
                        onload: (response) => {
                            try {
                                const data = JSON.parse(response.responseText);
                                if (data.words_result && data.words_result.length > 0) {
                                    // 提取验证码文本，只保留数字和字母
                                    const text = data.words_result[0].words.replace(/[^a-zA-Z0-9]/g, '');
                                    resolve(text);
                                } else {
                                    reject(new Error('未识别到验证码'));
                                }
                            } catch (error) {
                                reject(error);
                            }
                        },
                        onerror: (error) => {
                            reject(new Error('验证码识别请求失败'));
                        }
                    });
                });
            } catch (error) {
                console.error('验证码识别失败:', error);
                throw error;
            }
        }
    }

    // ==================== 自动学习机器人类 ====================
    class AutoLearningBot {
        constructor() {
            this.isRunning = false;
            this.currentAccount = null;
            this.settings = {};
            this.retryCount = 0;
            this.maxRetries = 3;
            this.pageHandlers = new Map();
            this.captchaRecognizer = new CaptchaRecognizer();

            this.init();
        }

        async init() {
            await Utils.waitForPageLoad();
            this.setupPageHandlers();
            await this.loadSettings();
            await this.detectAndHandlePage();

            window.logger.info('自动学习助手已初始化', 'system');
        }

        async loadSettings() {
            this.settings = await StorageManager.get('settings_data', {
                autoMute: true,
                autoSwitchCourseware: true,
                autoSwitchCourse: true,
                autoSelectCourse: true,
                courseCount: 10,
                delayTime: 1,
                baiduAK: '',
                baiduSK: ''
            });
        }

        // 获取延时时间（毫秒）
        getDelayTime() {
            return (this.settings.delayTime || 1) * 1000;
        }

        // 设置页面处理器
        setupPageHandlers() {
            this.pageHandlers.set('login', this.handleLoginPage.bind(this));
            this.pageHandlers.set('data', this.handleDataPage.bind(this));
            this.pageHandlers.set('courseMine', this.handleCoursePage.bind(this));
            // TODO: 实现以下页面处理器
            // this.pageHandlers.set('courseDetails', this.handleCourseDetailsPage.bind(this));
            // this.pageHandlers.set('videoChoose', this.handleVideoChoosePage.bind(this));
            // this.pageHandlers.set('video', this.handleVideoPage.bind(this));
            // this.pageHandlers.set('courseView', this.handleCourseViewPage.bind(this));
        }

        // 检测并处理当前页面
        async detectAndHandlePage() {
            const url = window.location.href;

            let pageType = 'unknown';

            if (url.includes('/index')) {
                pageType = 'login';
            } else if (url.includes('/study/data')) {
                pageType = 'data';
            } else if (url.includes('/study/courseMine')) {
                pageType = 'courseMine';
            } else if (url.includes('/courseDetailsNew')) {
                pageType = 'courseDetails';
            } else if (url.includes('/videoChoose')) {
                pageType = 'videoChoose';
            } else if (url.includes('/video?id=')) {
                pageType = 'video';
            } else if (url.includes('/study/course-view')) {
                pageType = 'courseView';
            }

            window.logger.success(`检测到页面类型: ${pageType}`, 'page');

            const handler = this.pageHandlers.get(pageType);
            if (handler && this.isRunning) {
                try {
                    await handler();
                } catch (error) {
                    window.logger.error(`页面处理失败: ${error.message}`, 'page');
                    await this.handleError(error);
                }
            }
        }

        // 开始账号处理
        async startAccountProcessing(account) {
            if (!account) {
                window.logger.error('没有账号信息', 'account');
                return;
            }

            this.currentAccount = account;
            this.isRunning = true;

            window.logger.info(`开始处理账号: ${account.phone}`, 'account');

            // 等待页面加载完成
            await Utils.sleep(this.getDelayTime());

            // 检查学习完成状态
            const completionStatus = await this.checkCompletionStatus();

            // 如果已完成，处理完成逻辑
            if (completionStatus === '已完成') {
                return;
            }

            // 检查当前页面并开始处理
            await this.detectAndHandlePage();
        }

        // 停止学习
        async stopLearning() {
            this.isRunning = false;
            this.currentAccount = null;
            window.logger.info('已停止学习', 'system');
        }

        // 处理错误
        async handleError(error) {
            window.logger.error(`处理错误: ${error.message}`, 'error');

            // 通知用户账号处理失败
            if (this.currentAccount) {
                window.logger.warning(`账号 ${this.currentAccount.phone} 学习未完成`, 'account');
                GM_notification({
                    text: `账号 ${this.currentAccount.phone} 学习失败: ${error.message}`,
                    title: '学习助手',
                    timeout: 5000
                });
            }
        }

        // 检查完成状态
        async checkCompletionStatus() {
            try {
                const statusElement = await Utils.waitForElement('.el-dropdown-menu__item.is-disabled div div:nth-child(5) b', 5000);
                const statusText = Utils.getText(statusElement);

                window.logger.info(`学习状态: ${statusText}`, 'data');

                // 如果学习已完成，处理账号完成逻辑
                if (statusText === '已完成') {
                    window.logger.success(`账号 ${this.currentAccount.phone} 学习已完成`, 'data');

                    GM_notification({
                        text: `账号 ${this.currentAccount.phone} 学习已完成！`,
                        title: '学习助手',
                        timeout: 5000
                    });

                    // 退出当前账号
                    await this.logout();
                    return statusText;
                }

                return statusText;
            } catch (error) {
                window.logger.warning('无法获取学习状态', 'data');
                return '未知';
            }
        }

        // 处理登录页面
        async handleLoginPage() {
            window.logger.info('处理登录页面', 'login');

            try {
                // 检查是否已经登录（页面自动跳转）
                await Utils.sleep(this.getDelayTime());
                if (window.location.href.includes('/study/data')) {
                    window.logger.info('检测到已登录，先退出账号', 'login');
                    await this.logout();
                    return;
                }

                // 等待登录表单加载
                await Utils.waitForElement('.loginCtr.flex-column.flex-around-center', 10000);

                // 填写账号信息
                await this.fillLoginForm();

                // 处理验证码
                await this.handleCaptcha();

                // 点击登录
                await this.clickLogin();

            } catch (error) {
                window.logger.error(`登录失败: ${error.message}`, 'login');
                throw error;
            }
        }

        // 填写登录表单
        async fillLoginForm() {
            if (!this.currentAccount) {
                throw new Error('没有当前账号信息');
            }

            // 填写手机号
            const phoneInput = await Utils.waitForElement('.el-input__inner[placeholder="您的手机号"]');
            await Utils.safeInput(phoneInput, this.currentAccount.phone);

            window.logger.success(`已填写手机号: ${this.currentAccount.phone}`, 'login');

            // 填写密码
            const passwordInput = await Utils.waitForElement('.el-input__inner[placeholder="请输入密码"]');
            await Utils.safeInput(passwordInput, this.currentAccount.password);

            window.logger.success('已填写密码', 'login');
        }

        // 处理验证码
        async handleCaptcha() {
            try {
                const captchaImg = await Utils.waitForElement('.yzmImg', 5000);
                const captchaInput = await Utils.waitForElement('.el-input__inner[placeholder="验证码"]');

                // 获取验证码图片的Base64
                const imageBase64 = await Utils.getImageBase64(captchaImg);

                window.logger.success('开始识别验证码', 'captcha');

                // 调用验证码识别服务
                const captchaText = await this.captchaRecognizer.recognizeCaptcha(
                    imageBase64,
                    this.settings.baiduAK,
                    this.settings.baiduSK
                );

                window.logger.success(`验证码识别结果: ${captchaText}`, 'captcha');

                // 填写验证码
                await Utils.safeInput(captchaInput, captchaText);

            } catch (error) {
                window.logger.warning(`验证码处理失败: ${error.message}`, 'captcha');

                // 重试机制
                if (this.retryCount < this.maxRetries) {
                    this.retryCount++;
                    window.logger.warning(`重试验证码识别 (${this.retryCount}/${this.maxRetries})`, 'captcha');

                    // 刷新验证码
                    const captchaImg = document.querySelector('.yzmImg');
                    if (captchaImg) {
                        await Utils.safeClick(captchaImg);
                        await Utils.sleep(this.getDelayTime());
                    }

                    await this.handleCaptcha();
                } else {
                    throw new Error('验证码识别失败次数过多');
                }
            }
        }

        // 点击登录
        async clickLogin() {
            const loginBtn = await Utils.waitForElement('.loginBtn.el-button');
            await Utils.safeClick(loginBtn);

            window.logger.success('已点击登录按钮', 'login');

            // 等待页面跳转
            try {
                await Utils.waitForUrlChange('/study/data', 10000);
                window.logger.success('登录成功', 'login');
                this.retryCount = 0; // 重置重试计数
            } catch (error) {
                // 检查是否有错误提示
                await Utils.sleep(this.getDelayTime());
                const errorMsg = await this.checkLoginError();
                if (errorMsg) {
                    throw new Error(`登录失败: ${errorMsg}`);
                } else {
                    throw new Error('登录超时');
                }
            }
        }

        // 检查登录错误
        async checkLoginError() {
            try {
                // 检查常见的错误提示元素
                const errorSelectors = [
                    '.el-message--error',
                    '.el-notification__content',
                    '.error-message'
                ];

                for (const selector of errorSelectors) {
                    const errorElement = document.querySelector(selector);
                    if (errorElement && errorElement.textContent.trim()) {
                        return errorElement.textContent.trim();
                    }
                }

                return null;
            } catch (error) {
                return null;
            }
        }

        // 退出登录
        async logout() {
            try {
                // 查找退出按钮
                const logoutBtn = await Utils.waitForElement('.el-dropdown-menu__item.flex-between-center button:nth-child(2)', 5000);
                await Utils.safeClick(logoutBtn);

                window.logger.success('已点击退出按钮', 'logout');

                // 等待跳转到登录页
                await Utils.waitForUrlChange('/index', 5000);

                window.logger.success('已成功退出登录', 'logout');

            } catch (error) {
                window.logger.warning(`退出登录失败: ${error.message}`, 'logout');
                // 直接跳转到登录页作为备用方案
                window.location.href = 'https://study.jxgbwlxy.gov.cn/index';
            }
        }

        // 处理学习档案页面
        async handleDataPage() {
            window.logger.success('处理学习档案页面', 'data');

            try {
                // 等待页面加载完成
                await Utils.sleep(this.getDelayTime());

                // 检查学习状态，如果已完成会自动处理退出逻辑
                const completionStatus = await this.checkCompletionStatus();

                // 如果已完成，checkCompletionStatus 会处理退出，这里直接返回
                if (completionStatus === '已完成') {
                    return;
                }

                window.logger.success('学习未完成，跳转到我的课程', 'data');

                // 点击"我的课程"菜单
                await this.navigateToMyCourses();

            } catch (error) {
                window.logger.error(`处理学习档案页面失败: ${error.message}`, 'data');
                throw error;
            }
        }

        // 导航到我的课程
        async navigateToMyCourses() {
            const myCourseMenu = await Utils.waitForElement('a[href="/study/courseMine?id=0"] .menuLabel');
            await Utils.safeClick(myCourseMenu);

            // 等待页面跳转
            await Utils.waitForUrlChange('/study/courseMine', 10000);
        }

        // 处理我的课程页面
        async handleCoursePage() {
            window.logger.success('处理我的课程页面', 'course');

            try {
                // 等待页面加载
                await Utils.sleep(this.getDelayTime());

                // 先处理必修课
                const hasRequiredCourse = await this.handleRequiredCourses();

                // 只有在必修课全部完成后才处理选修课
                if (!hasRequiredCourse) {
                    await this.handleElectiveCourses();
                }

            } catch (error) {
                window.logger.error(`处理课程页面失败: ${error.message}`, 'course');
                throw error;
            }
        }

        // 处理必修课
        async handleRequiredCourses() {
            window.logger.success('处理必修课', 'course');

            // 点击必修课选项卡
            const requiredTab = await Utils.waitForElement('.menu_item .ellipsis', 5000);
            if (requiredTab && requiredTab.textContent.includes('必修')) {
                await Utils.safeClick(requiredTab);
                await Utils.sleep(this.getDelayTime());
            }

            // 查找未完成的课程
            const incompleteCourse = await this.findIncompleteCourse();
            if (incompleteCourse) {
                window.logger.success('找到未完成的必修课，开始学习', 'course');
                await Utils.safeClick(incompleteCourse);
                return true;
            }

            window.logger.success('必修课已全部完成', 'course');
            return false;
        }

        // 查找未完成的课程
        async findIncompleteCourse() {
            try {
                const courseCards = Utils.getAllElements('.courseCard.shadow');

                for (const card of courseCards) {
                    const statusElement = card.querySelector('.courseCard .red');
                    if (statusElement && statusElement.textContent.includes('未完成')) {
                        return card;
                    }
                }

                return null;
            } catch (error) {
                window.logger.warning(`查找未完成课程失败: ${error.message}`, 'course');
                return null;
            }
        }
    }

    // ==================== 用户界面管理类 ====================
    class UIManager {
        constructor() {
            this.panel = null;
            this.currentTab = 'accounts';
            this.accounts = [];
            this.settings = {};
            this.menuCommands = [];

            this.init();
        }

        async init() {
            await this.loadData();
            this.setupMenuCommands();
        }

        async loadData() {
            this.accounts = await StorageManager.get('accounts_data', []);
            this.settings = await StorageManager.get('settings_data', {
                autoMute: true,
                autoSwitchCourseware: true,
                autoSwitchCourse: true,
                autoSelectCourse: true,
                courseCount: 10,
                delayTime: 1,
                baiduAK: '',
                baiduSK: ''
            });
        }

        async saveData() {
            await StorageManager.set('accounts_data', this.accounts);
            await StorageManager.set('settings_data', this.settings);
        }

        setupMenuCommands() {
            // 清除现有菜单命令
            this.menuCommands.forEach(id => GM_unregisterMenuCommand(id));
            this.menuCommands = [];

            // 注册菜单命令
            this.menuCommands.push(
                GM_registerMenuCommand('📚 开始学习', () => this.startLearning(), 's'),
                GM_registerMenuCommand('⏹️ 停止学习', () => this.stopLearning(), 't'),
                GM_registerMenuCommand('👥 账号管理', () => this.showPanel('accounts'), 'a'),
                GM_registerMenuCommand('⚙️ 设置配置', () => this.showPanel('settings'), 'c'),
                GM_registerMenuCommand('📋 查看日志', () => this.showPanel('logs'), 'l'),
                GM_registerMenuCommand('❌ 关闭面板', () => this.hidePanel(), 'x')
            );
        }

        showPanel(tab = 'accounts') {
            this.currentTab = tab;

            if (this.panel) {
                this.hidePanel();
            }

            this.panel = document.createElement('div');
            this.panel.className = 'auto-learning-panel';
            this.panel.innerHTML = this.getPanelHTML();

            document.body.appendChild(this.panel);

            // 绑定事件
            this.bindEvents();

            // 更新显示
            this.updateTabContent();
        }

        hidePanel() {
            if (this.panel) {
                this.panel.remove();
                this.panel = null;
            }
        }

        getPanelHTML() {
            return `
                <div class="auto-learning-header">
                    <span>江西干部网络学院学习助手</span>
                    <button class="auto-learning-close" onclick="this.closest('.auto-learning-panel').remove()">×</button>
                </div>
                <div class="auto-learning-content">
                    <div class="auto-learning-tabs">
                        <div class="auto-learning-tab ${this.currentTab === 'accounts' ? 'active' : ''}" data-tab="accounts">账号管理</div>
                        <div class="auto-learning-tab ${this.currentTab === 'settings' ? 'active' : ''}" data-tab="settings">设置配置</div>
                        <div class="auto-learning-tab ${this.currentTab === 'logs' ? 'active' : ''}" data-tab="logs">运行日志</div>
                    </div>
                    <div class="auto-learning-tab-content">
                        <!-- 内容将通过 updateTabContent 动态更新 -->
                    </div>
                </div>
            `;
        }

        bindEvents() {
            if (!this.panel) return;

            // 标签页切换
            this.panel.querySelectorAll('.auto-learning-tab').forEach(tab => {
                tab.addEventListener('click', (e) => {
                    const tabName = e.target.dataset.tab;
                    this.switchTab(tabName);
                });
            });

            // 关闭按钮
            const closeBtn = this.panel.querySelector('.auto-learning-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.hidePanel());
            }
        }

        switchTab(tabName) {
            this.currentTab = tabName;

            // 更新标签页样式
            this.panel.querySelectorAll('.auto-learning-tab').forEach(tab => {
                tab.classList.toggle('active', tab.dataset.tab === tabName);
            });

            // 更新内容
            this.updateTabContent();
        }

        updateTabContent() {
            if (!this.panel) return;

            const contentContainer = this.panel.querySelector('.auto-learning-tab-content');
            if (!contentContainer) return;

            switch (this.currentTab) {
                case 'accounts':
                    contentContainer.innerHTML = this.getAccountsHTML();
                    this.bindAccountsEvents();
                    break;
                case 'settings':
                    contentContainer.innerHTML = this.getSettingsHTML();
                    this.bindSettingsEvents();
                    break;
                case 'logs':
                    contentContainer.innerHTML = this.getLogsHTML();
                    window.logger.updateLogDisplay();
                    break;
            }
        }

        getAccountsHTML() {
            return `
                <div class="auto-learning-form-group">
                    <div class="auto-learning-row">
                        <div class="auto-learning-col">
                            <input type="text" class="auto-learning-input" id="account-phone" placeholder="手机号">
                        </div>
                        <div class="auto-learning-col">
                            <input type="password" class="auto-learning-input" id="account-password" placeholder="密码">
                        </div>
                        <button class="auto-learning-button" id="add-account">添加账号</button>
                    </div>
                </div>

                <div class="auto-learning-accounts-list">
                    ${this.accounts.map((account, index) => `
                        <div class="auto-learning-account-item" data-index="${index}">
                            <div class="auto-learning-account-phone">${account.phone}</div>
                            <div class="auto-learning-row">
                                <span class="auto-learning-account-status auto-learning-status-${account.status || 'pending'}">
                                    ${this.getStatusText(account.status)}
                                </span>
                                <button class="auto-learning-button danger" onclick="window.uiManager.removeAccount(${index})">删除</button>
                            </div>
                        </div>
                    `).join('')}
                </div>

                <div class="auto-learning-form-group">
                    <button class="auto-learning-button" id="start-learning">开始学习</button>
                    <button class="auto-learning-button danger" id="stop-learning">停止学习</button>
                    <button class="auto-learning-button" id="clear-accounts">清空账号</button>
                </div>
            `;
        }

        getStatusText(status) {
            const statusMap = {
                'pending': '等待中',
                'running': '运行中',
                'completed': '已完成',
                'error': '出错'
            };
            return statusMap[status] || '等待中';
        }

        getSettingsHTML() {
            return `
                <div class="auto-learning-form-group">
                    <label class="auto-learning-label">百度OCR API Key</label>
                    <input type="text" class="auto-learning-input" id="baidu-ak" value="${this.settings.baiduAK || ''}" placeholder="请输入百度API Key">
                </div>

                <div class="auto-learning-form-group">
                    <label class="auto-learning-label">百度OCR Secret Key</label>
                    <input type="password" class="auto-learning-input" id="baidu-sk" value="${this.settings.baiduSK || ''}" placeholder="请输入百度Secret Key">
                </div>

                <div class="auto-learning-form-group">
                    <label class="auto-learning-label">操作延时时间（秒）</label>
                    <input type="number" class="auto-learning-input" id="delay-time" value="${this.settings.delayTime || 1}" min="0.5" max="10" step="0.5">
                </div>

                <div class="auto-learning-form-group">
                    <label class="auto-learning-label">自动选修课程数量</label>
                    <input type="number" class="auto-learning-input" id="course-count" value="${this.settings.courseCount || 10}" min="1" max="50">
                </div>

                <div class="auto-learning-form-group">
                    <div class="auto-learning-row">
                        <label class="auto-learning-checkbox">
                            <input type="checkbox" id="auto-mute" ${this.settings.autoMute ? 'checked' : ''}>
                            自动静音视频
                        </label>
                    </div>
                </div>

                <div class="auto-learning-form-group">
                    <div class="auto-learning-row">
                        <label class="auto-learning-checkbox">
                            <input type="checkbox" id="auto-select-course" ${this.settings.autoSelectCourse ? 'checked' : ''}>
                            自动添加选修课程
                        </label>
                    </div>
                </div>

                <div class="auto-learning-form-group">
                    <button class="auto-learning-button" id="save-settings">保存设置</button>
                    <button class="auto-learning-button danger" id="reset-settings">重置设置</button>
                </div>
            `;
        }

        getLogsHTML() {
            return `
                <div class="auto-learning-form-group">
                    <button class="auto-learning-button" id="clear-logs">清空日志</button>
                    <button class="auto-learning-button" id="export-logs">导出日志</button>
                </div>
                <div class="auto-learning-log"></div>
            `;
        }

        bindAccountsEvents() {
            if (!this.panel) return;

            // 添加账号
            const addBtn = this.panel.querySelector('#add-account');
            if (addBtn) {
                addBtn.addEventListener('click', () => this.addAccount());
            }

            // 开始学习
            const startBtn = this.panel.querySelector('#start-learning');
            if (startBtn) {
                startBtn.addEventListener('click', () => this.startLearning());
            }

            // 停止学习
            const stopBtn = this.panel.querySelector('#stop-learning');
            if (stopBtn) {
                stopBtn.addEventListener('click', () => this.stopLearning());
            }

            // 清空账号
            const clearBtn = this.panel.querySelector('#clear-accounts');
            if (clearBtn) {
                clearBtn.addEventListener('click', () => this.clearAccounts());
            }
        }

        bindSettingsEvents() {
            if (!this.panel) return;

            // 保存设置
            const saveBtn = this.panel.querySelector('#save-settings');
            if (saveBtn) {
                saveBtn.addEventListener('click', () => this.saveSettings());
            }

            // 重置设置
            const resetBtn = this.panel.querySelector('#reset-settings');
            if (resetBtn) {
                resetBtn.addEventListener('click', () => this.resetSettings());
            }
        }

        async addAccount() {
            const phoneInput = this.panel.querySelector('#account-phone');
            const passwordInput = this.panel.querySelector('#account-password');

            if (!phoneInput || !passwordInput) return;

            const phone = phoneInput.value.trim();
            const password = passwordInput.value.trim();

            if (!phone || !password) {
                GM_notification({
                    text: '请填写完整的账号信息',
                    title: '学习助手',
                    timeout: 3000
                });
                return;
            }

            // 检查是否已存在
            if (this.accounts.some(acc => acc.phone === phone)) {
                GM_notification({
                    text: '该手机号已存在',
                    title: '学习助手',
                    timeout: 3000
                });
                return;
            }

            // 添加账号
            this.accounts.push({
                phone,
                password,
                status: 'pending',
                addTime: Date.now()
            });

            await this.saveData();

            // 清空输入框
            phoneInput.value = '';
            passwordInput.value = '';

            // 更新显示
            this.updateTabContent();

            window.logger.success(`已添加账号: ${phone}`, 'account');
        }

        async removeAccount(index) {
            if (index >= 0 && index < this.accounts.length) {
                const account = this.accounts[index];
                this.accounts.splice(index, 1);
                await this.saveData();
                this.updateTabContent();
                window.logger.info(`已删除账号: ${account.phone}`, 'account');
            }
        }

        async startLearning() {
            if (this.accounts.length === 0) {
                GM_notification({
                    text: '请先添加账号',
                    title: '学习助手',
                    timeout: 3000
                });
                return;
            }

            if (!this.settings.baiduAK || !this.settings.baiduSK) {
                GM_notification({
                    text: '请先配置百度OCR API',
                    title: '学习助手',
                    timeout: 3000
                });
                this.showPanel('settings');
                return;
            }

            // 找到第一个未完成的账号
            const nextAccount = this.accounts.find(acc => acc.status !== 'completed');
            if (!nextAccount) {
                GM_notification({
                    text: '所有账号已完成学习',
                    title: '学习助手',
                    timeout: 3000
                });
                return;
            }

            // 更新账号状态
            nextAccount.status = 'running';
            await this.saveData();

            // 开始学习
            if (window.autoLearningBot) {
                await window.autoLearningBot.startAccountProcessing(nextAccount);
            }

            this.updateTabContent();
            window.logger.info(`开始学习账号: ${nextAccount.phone}`, 'system');
        }

        async stopLearning() {
            if (window.autoLearningBot) {
                await window.autoLearningBot.stopLearning();
            }

            // 重置所有运行中的账号状态
            this.accounts.forEach(acc => {
                if (acc.status === 'running') {
                    acc.status = 'pending';
                }
            });

            await this.saveData();
            this.updateTabContent();

            GM_notification({
                text: '已停止学习',
                title: '学习助手',
                timeout: 3000
            });

            window.logger.info('已停止学习', 'system');
        }

        async saveSettings() {
            if (!this.panel) return;

            // 获取设置值
            const baiduAK = this.panel.querySelector('#baidu-ak')?.value || '';
            const baiduSK = this.panel.querySelector('#baidu-sk')?.value || '';
            const delayTime = parseFloat(this.panel.querySelector('#delay-time')?.value) || 1;
            const courseCount = parseInt(this.panel.querySelector('#course-count')?.value) || 10;
            const autoMute = this.panel.querySelector('#auto-mute')?.checked || false;
            const autoSelectCourse = this.panel.querySelector('#auto-select-course')?.checked || false;

            // 更新设置
            this.settings = {
                ...this.settings,
                baiduAK,
                baiduSK,
                delayTime,
                courseCount,
                autoMute,
                autoSelectCourse
            };

            await this.saveData();

            // 更新机器人设置
            if (window.autoLearningBot) {
                window.autoLearningBot.settings = this.settings;
            }

            GM_notification({
                text: '设置已保存',
                title: '学习助手',
                timeout: 3000
            });

            window.logger.success('设置已保存', 'settings');
        }

        async resetSettings() {
            if (confirm('确定要重置所有设置吗？')) {
                this.settings = {
                    autoMute: true,
                    autoSwitchCourseware: true,
                    autoSwitchCourse: true,
                    autoSelectCourse: true,
                    courseCount: 10,
                    delayTime: 1,
                    baiduAK: '',
                    baiduSK: ''
                };

                await this.saveData();
                this.updateTabContent();

                GM_notification({
                    text: '设置已重置',
                    title: '学习助手',
                    timeout: 3000
                });

                window.logger.info('设置已重置', 'settings');
            }
        }

        async clearAccounts() {
            if (confirm('确定要清空所有账号吗？')) {
                this.accounts = [];
                await this.saveData();
                this.updateTabContent();
                window.logger.info('已清空所有账号', 'account');
            }
        }
    }

    // 全局实例
    window.logger = new Logger();
    window.utils = Utils;
    window.autoLearningBot = new AutoLearningBot();
    window.uiManager = new UIManager();

})();
