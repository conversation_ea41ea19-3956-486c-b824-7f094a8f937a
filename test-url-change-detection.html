<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URL变化检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #3b82f6;
        }
        .current-url {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
        .log-warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>URL变化检测测试</h1>
        <p>这个测试页面用于验证URL变化监听器是否正常工作。</p>
        
        <div>
            <strong>当前URL:</strong>
            <div id="currentUrl" class="current-url"></div>
        </div>
        
        <button id="testPushState" class="test-button">测试 pushState</button>
        <button id="testReplaceState" class="test-button">测试 replaceState</button>
        <button id="testHashChange" class="test-button">测试 Hash 变化</button>
        <button id="clearLog" class="test-button">清空日志</button>
        
        <div id="logArea" class="log-area"></div>
    </div>

    <script>
        let currentUrl = window.location.href;
        
        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 更新当前URL显示
        function updateCurrentUrl() {
            document.getElementById('currentUrl').textContent = window.location.href;
        }

        // 模拟脚本的URL变化监听器
        function setupUrlChangeListener() {
            // 监听popstate事件（浏览器前进后退）
            window.addEventListener('popstate', async () => {
                if (currentUrl !== window.location.href) {
                    currentUrl = window.location.href;
                    log(`检测到URL变化(popstate): ${currentUrl}`, 'info');
                    updateCurrentUrl();
                }
            });

            // 监听pushstate和replacestate（程序化导航）
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;
            
            history.pushState = function(...args) {
                originalPushState.apply(history, args);
                setTimeout(() => {
                    if (currentUrl !== window.location.href) {
                        currentUrl = window.location.href;
                        log(`检测到URL变化(pushState): ${currentUrl}`, 'success');
                        updateCurrentUrl();
                    }
                }, 100);
            };
            
            history.replaceState = function(...args) {
                originalReplaceState.apply(history, args);
                setTimeout(() => {
                    if (currentUrl !== window.location.href) {
                        currentUrl = window.location.href;
                        log(`检测到URL变化(replaceState): ${currentUrl}`, 'success');
                        updateCurrentUrl();
                    }
                }, 100);
            };

            // 定期检查URL变化（作为备用方案）
            setInterval(() => {
                if (currentUrl !== window.location.href) {
                    currentUrl = window.location.href;
                    log(`检测到URL变化(定期检查): ${currentUrl}`, 'warning');
                    updateCurrentUrl();
                }
            }, 3000);
        }

        // 测试pushState
        document.getElementById('testPushState').addEventListener('click', function() {
            const newUrl = window.location.origin + window.location.pathname + '?test=pushstate&time=' + Date.now();
            history.pushState({test: 'pushstate'}, 'Test PushState', newUrl);
            log('执行了 history.pushState', 'info');
        });

        // 测试replaceState
        document.getElementById('testReplaceState').addEventListener('click', function() {
            const newUrl = window.location.origin + window.location.pathname + '?test=replacestate&time=' + Date.now();
            history.replaceState({test: 'replacestate'}, 'Test ReplaceState', newUrl);
            log('执行了 history.replaceState', 'info');
        });

        // 测试Hash变化
        document.getElementById('testHashChange').addEventListener('click', function() {
            window.location.hash = '#test-' + Date.now();
            log('修改了 location.hash', 'info');
        });

        // 清空日志
        document.getElementById('clearLog').addEventListener('click', function() {
            document.getElementById('logArea').innerHTML = '';
        });

        // 初始化
        updateCurrentUrl();
        setupUrlChangeListener();
        log('URL变化监听器已设置', 'success');
        log('点击上方按钮测试不同的URL变化方式', 'info');
    </script>
</body>
</html>
