<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MouseEvent修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .test-button {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #3b82f6;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>MouseEvent修复测试</h1>
        <p>这个测试页面用于验证MouseEvent构造函数的修复是否有效。</p>
        
        <button id="testOldMethod" class="test-button">测试旧方法 (带view参数)</button>
        <button id="testNewMethod" class="test-button">测试新方法 (不带view参数)</button>
        <button id="testSafeClick" class="test-button">测试safeClick方法</button>
        <button id="clearLog" class="test-button">清空日志</button>
        
        <div id="logArea" class="log-area"></div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 测试旧方法（带view参数）
        document.getElementById('testOldMethod').addEventListener('click', function() {
            try {
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                log('旧方法测试成功：MouseEvent with view parameter created', 'success');
            } catch (error) {
                log(`旧方法测试失败：${error.message}`, 'error');
            }
        });

        // 测试新方法（不带view参数）
        document.getElementById('testNewMethod').addEventListener('click', function() {
            try {
                const event = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true
                });
                log('新方法测试成功：MouseEvent without view parameter created', 'success');
            } catch (error) {
                log(`新方法测试失败：${error.message}`, 'error');
            }
        });

        // 模拟safeClick方法
        async function safeClick(element) {
            if (!element) throw new Error('元素不存在');

            // 滚动到元素位置
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            await new Promise(resolve => setTimeout(resolve, 500));

            // 触发点击事件 - 使用修复后的方法
            const event = new MouseEvent('click', {
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        }

        // 测试safeClick方法
        document.getElementById('testSafeClick').addEventListener('click', async function() {
            try {
                const testElement = document.createElement('button');
                testElement.textContent = '测试元素';
                testElement.addEventListener('click', () => {
                    log('safeClick触发的点击事件成功执行', 'success');
                });
                
                await safeClick(testElement);
                log('safeClick方法测试成功', 'success');
            } catch (error) {
                log(`safeClick方法测试失败：${error.message}`, 'error');
            }
        });

        // 清空日志
        document.getElementById('clearLog').addEventListener('click', function() {
            document.getElementById('logArea').innerHTML = '';
        });

        // 初始化日志
        log('MouseEvent修复测试页面已加载', 'info');
        log('点击上方按钮进行测试', 'info');
    </script>
</body>
</html>
